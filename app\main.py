import base64
import os
import sys
import time
import asyncio
from pathlib import Path
from dotenv import load_dotenv
from PIL import Image
import io
import requests

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

# 首先配置日志 - 必须在导入其他模块之前完成
from app.logging_config import setup_logging, set_layer_context, set_operation_context
setup_logging()
import logging
logger = logging.getLogger('app')  # Use 'app' logger

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity,
    ValidationException, ExternalServiceException, ConfigurationException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary

# Set layer context for presentation layer
set_layer_context("presentation")

# 现在导入其他模块
from app.api.client import RAGWorkflowClient, ReportAgentClient

# 加载环境变量（覆盖现有变量）
load_dotenv(override=True)
# 调试环境变量
logger.info("DEBUG - 初始化Streamlit前的环境变量:")
logger.info(f"OPENAI_API_BASE: {os.getenv('OPENAI_API_BASE')}")
logger.info(f"OPENAI_API_KEY: {'*' * len(os.getenv('OPENAI_API_KEY', ''))}")
logger.info(f"MODEL_ID: {os.getenv('MODEL_ID')}")

# 首先配置Streamlit以避免模块检查问题
import streamlit as st
from streamlit import config as st_config
st_config.set_option('server.fileWatcherType', 'none')
st_config.set_option('server.enableCORS', False)
st_config.set_option('server.enableXsrfProtection', False)
st_config.set_option('runner.magicEnabled', False)

# 现在导入应用组件
from app.config import AgentConfig
from crawler.client import CrawlerClient

@handle_layer_boundary(LayerType.PRESENTATION, "crawler status check")
async def check_crawler_status():
    """仅检查爬虫状态，不执行登录操作 with unified error handling"""
    set_operation_context("crawler_status_check")

    with error_boundary("crawler client initialization", LayerType.PRESENTATION):
        try:
            client = CrawlerClient()
        except Exception as e:
            raise ExternalServiceException(
                message="Failed to initialize crawler client",
                service_name="CrawlerClient",
                details=f"Client initialization error: {str(e)}",
                original_exception=e,
                suggested_action="Check crawler service configuration"
            )

    with error_boundary("crawler status retrieval", LayerType.PRESENTATION):
        try:
            # 只获取状态，不执行登录
            result = await client.get_status()

            # 构建状态字典
            status_info = {
                'logged_in': result.get('logged_in', False),
                'message': result.get('message', '状态未知'),
                'status': result.get('status', 'unknown'),
                'screen': result.get('screen', None)[:20] if result.get('screen', None) else None
            }
            logger.debug(f"爬虫状态: {status_info}")

            return status_info

        except Exception as e:
            raise ExternalServiceException(
                message="Failed to get crawler status",
                service_name="crawler_service",
                details=f"Status retrieval error: {str(e)}",
                original_exception=e,
                suggested_action="Check crawler service connectivity"
            )
        finally:
            try:
                await client.close()
            except Exception as e:
                logger.warning(f"Failed to close crawler client: {str(e)}")

@handle_layer_boundary(LayerType.PRESENTATION, "crawler login execution")
async def perform_crawler_login():
    """执行爬虫登录操作 with unified error handling"""
    set_operation_context("crawler_login")

    with error_boundary("crawler client initialization", LayerType.PRESENTATION):
        try:
            client = CrawlerClient()
        except Exception as e:
            raise ExternalServiceException(
                message="Failed to initialize crawler client for login",
                service_name="CrawlerClient",
                details=f"Client initialization error: {str(e)}",
                original_exception=e,
                suggested_action="Check crawler service configuration"
            )

    with error_boundary("crawler login execution", LayerType.PRESENTATION):
        try:
            # 执行登录操作，超时时间120秒
            result = await client.login(120.0)
            logger.debug(f"登录结果: {result}")

            # 构建登录状态字典
            login_status = {
                **result,
                'logged_in': result.get('logged_in', False),
                'message': '登录成功' if result.get('logged_in') else '需要登录',
                'action': result.get('message', ''),
                'status': result.get('status', '')
            }
            logger.info(f"登录操作结果: {login_status}")

            return login_status

        except Exception as e:
            raise ExternalServiceException(
                message="Failed to perform crawler login",
                service_name="crawler_service",
                details=f"Login error: {str(e)}",
                original_exception=e,
                suggested_action="Check crawler service status and credentials"
            )
        finally:
            try:
                await client.close()
            except Exception as e:
                logger.warning(f"Failed to close crawler client: {str(e)}")

async def refresh_crawler_screenshot():
    """刷新爬虫截图"""
    client = CrawlerClient()
    try:
        # 获取当前截图
        screen = await client.get_screenshot("./screenshot.png")
        logger.debug("截图已刷新")
        return screen
    except Exception as e:
        logger.warning(f"截图刷新失败: {str(e)}")
        return None
    finally:
        await client.close()

def check_elements_complete(current_values, agent):
    """
    检查所有报告要素是否齐全
    
    参数:
        current_values: 当前UI状态值
        agent: 报告代理对象
        
    返回:
        tuple: (是否齐全, 缺失要素字典)
    """
    # 必需的基本要素
    required = {
        'company': current_values['company'],  # 公司名称
        'year': current_values['year']         # 报告年份
    }
    # 找出缺失的要素
    missing = {k: "未设置" for k, v in required.items() if not v}
    
    # 检查资料文件是否齐全
    if current_values['company'] and current_values['year']:
        # 构建工作区路径
        workspace_path = os.path.join(
            "workspaces",
            agent.config.workspace_id,
            current_values['company'],
            str(current_values['year'])
        )
        
        # 检查工作区是否存在
        if os.path.exists(workspace_path):
            # 获取所有支持的资料文件
            materials = [
                f for f in os.listdir(workspace_path)
                if f.lower().endswith(('.pdf', '.xls', '.xlsx'))
            ]
            # 如果没有资料文件且未确认无需资料
            if not materials and not agent.required_files['confirmed_empty']:
                missing['materials'] = "未上传或确认"
    else:
        # 公司或年份未设置时标记资料缺失
        missing['materials'] = "缺失"
        
    # 返回检查结果：是否所有要素齐全，以及缺失要素字典
    return len(missing) == 0, missing

def confirm_generation(user_input):
    """
    检查用户输入是否为确认生成报告
    
    参数:
        user_input: 用户输入的文本
        
    返回:
        bool: 是否为确认指令
    """
    # 定义确认关键词集合
    confirm_keywords = {
        'true', '是', 'yes', 'y', 'go',
        '确认', '同意', 'ok', '好的', '可以了', '对', '行',
        'continue', 'proceed', 'confirm', '继续', '开始',
        '重试', '再来一次', '再来', '好了', '行了', '要得了', '好', '行'
    }
    # 检查用户输入是否包含确认关键词
    return user_input.strip().lower() in confirm_keywords

@handle_layer_boundary(LayerType.PRESENTATION, "RAG workflow execution")
async def execute_rag_workflow():
    """
    执行RAG工作流并返回响应字典
    
    返回:
        dict: 包含状态、消息和报告路径的响应字典
    """
    # 准备工作区路径
    workspace_path = os.path.join(
        "workspaces",
        st.session_state.agent.config.workspace_id,
        st.session_state.current_values['company'],
        str(st.session_state.current_values['year'])
    )
    
    # 确保工作区存在
    os.makedirs(workspace_path, exist_ok=True)
    logger.debug(f"确保工作区输出目录存在: {workspace_path}")
    logger.debug(f"当前用户工作区ID: {st.session_state.agent.config.workspace_id}")
    logger.debug(f"发送的工作区路径: {workspace_path}")
    
    # 初始化响应字典
    response = {}
    with st.spinner("正在生成报告..."):
        # Create ClientConfig for RAGWorkflowClient
        from app.api.client import ClientConfig
        api_base_url = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
        client_config = ClientConfig(
            base_url=api_base_url,
            auth_token=st.session_state.access_token
        )
        
        # Create RAG工作流客户端
        rag_client = RAGWorkflowClient(client_config)
        
        # 执行RAG工作流
        result = rag_client.execute(
            workspace_path=workspace_path,
            template_file=st.session_state.current_values['template_file']
        )
        logger.debug(f"RAG工作流结果: {result}")
        
        if result.get('status') == 'error':
            # 处理错误响应
            error_msg = f"报告生成遇到问题\n\n错误代码: {result['code']}\n{result['message']}"
            if result.get('details'):
                error_msg += f"\n\n技术详情: {result['details']}"
            if result.get('action'):
                error_msg += f"\n\n建议操作: {result['action']}"
            else:
                error_msg += "\n\n建议操作: 请检查输入并重试"
            
            response = {
                'message': error_msg,
                'status': 'error',
                'code': result['code']
            }
        else:
            # 处理成功情况
            report_path = result.get('report_path', None)
            
            if report_path and os.path.exists(report_path):
                # 扫描新生成的报告
                from app.api.client import ClientConfig
                api_base_url = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
                client_config = ClientConfig(
                    base_url=api_base_url,
                    auth_token=st.session_state.access_token
                )
                
                report_client = ReportAgentClient(client_config)
                try:
                    scan_result = report_client.scan_reports(st.session_state.agent.config.workspace_id)
                    if scan_result.get('status') == 'success':
                        st.session_state.available_reports = scan_result['available_reports']
                    else:
                        logger.warning(f"扫描报告失败: {scan_result.get('message', '未知错误')}")
                        st.session_state.available_reports = []
                except Exception as e:
                    logger.error(f"扫描报告错误: {str(e)}")
                    st.session_state.available_reports = []
                
                message = f"报告生成成功！\n\n会话已重置，可以开始新的任务"
                
                response = {
                    'message': message,
                    'status': 'success', 
                    'report_path': report_path,
                    'scan_result': scan_result
                }
            else:
                # 报告文件不存在时的错误处理
                response = {
                    'message': f"报告生成失败: 未生成有效报告文件{ ' - ' + report_path if report_path else ''}",
                    'status': 'error',
                    'code': 'ERR999'
                }
    
    # 确保总是返回response
    return response

def session_state_reset():
    """重置会话状态但保留UI元素和模板选择"""
    # Preserve template selection
    current_template = st.session_state.current_values.get('template_file')
    
    st.session_state.current_values = {
        'company': None,
        'year': None,
        'quarter': None,
        'template_file': current_template,  # Keep existing template
        'materials_count': 0,
        'materials_status': 'missing',
        'missing_elements': None,
        'conversation_history': [],
        'conversation_phase': 'initial',
    }
    
    # Keep UI elements visible
    st.session_state.setdefault('show_elements', True)
    
    config = {
                "workspace_id": st.session_state.department
            }
    st.session_state.agent = type('Agent', (), {})()  # 创建动态对象
    agent = st.session_state.agent
    agent.config = AgentConfig(**config)
    agent.agent = None

    st.session_state.chat_history = []
    st.session_state.confirmation_pending = False
    st.session_state.processed_files = set()
    st.session_state.uploaded_files = []

# Authentication functions
def login_user(username, password):
    """Login user and return access token"""
    try:
        API_BASE = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
        
        # Prepare form data
        form_data = {
            'username': username,
            'password': password
        }
        
        response = requests.post(f"{API_BASE}/token", data=form_data)
        
        if response.status_code == 200:
            token_data = response.json()
            
            # Get user info to retrieve department
            headers = {'Authorization': f'Bearer {token_data["access_token"]}'}
            user_response = requests.get(f"{API_BASE}/user/me", headers=headers)
            
            if user_response.status_code == 200:
                user_info = user_response.json()
                return {
                    'success': True,
                    'access_token': token_data['access_token'],
                    'username': username,
                    'department': user_info.get('department', 'default_workspace')
                }
            else:
                return {
                    'success': True,
                    'access_token': token_data['access_token'],
                    'username': username,
                    'department': 'default_workspace'  # Fallback
                }
        else:
            return {
                'success': False,
                'error': f"登录失败: {response.text}"
            }
    except Exception as e:
        return {
            'success': False,
            'error': f"网络错误: {str(e)}"
        }

def get_user_templates(access_token):
    """Get templates for authenticated user"""
    try:
        API_BASE = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
        
        headers = {'Authorization': f'Bearer {access_token}'}
        response = requests.get(f"{API_BASE}/templates", headers=headers)
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.warning(f"Failed to get templates: {response.status_code}")
            return []
    except Exception as e:
        logger.error(f"Error getting templates: {str(e)}")
        return []

def show_login_page():
    """Display login page"""
    st.title("智能报告生成系统")
    st.subheader("用户登录")
    
    # Create login form
    with st.form("login_form"):
        username = st.text_input("用户名", placeholder="请输入用户名")
        password = st.text_input("密码", type="password", placeholder="请输入密码")
        submit_button = st.form_submit_button("登录")
        
        if submit_button:
            if username and password:
                with st.spinner("正在登录..."):
                    result = login_user(username, password)
                    
                if result['success']:
                    st.session_state.authenticated = True
                    st.session_state.access_token = result['access_token']
                    st.session_state.username = result['username']
                    st.session_state.department = result['department']
                    st.success("登录成功！")
                    st.rerun()
                else:
                    st.error(result['error'])
            else:
                st.error("请输入用户名和密码")
    
    # Show sample users
    with st.expander("示例用户", expanded=True):
        st.markdown("""
        **可用的测试用户：**
        - **test** / test123 (test_workspace)
        - **user1** / user123 (dept_finance)  
        - **user2** / user123 (dept_risk)
        - **admin** / admin123 (admin_workspace)
        """)

def show_template_manager():
    """Display template management page"""
    st.title("模板管理")
    
    # Back button
    if st.button("← 返回主页"):
        st.session_state.show_template_manager = False
        st.rerun()
    
    st.divider()
    
    # Create new template
    st.subheader("创建新模板")
    
    # Template upload option
    st.write("**方式一：上传模板文件**")
    uploaded_template = st.file_uploader("上传模板文件", type=['md'], help="选择.md格式的模板文件")
    
    if uploaded_template:
        template_name = uploaded_template.name.replace('.md', '')
        template_content = uploaded_template.read().decode('utf-8')
        
        if st.button("上传模板"):
            try:
                API_BASE = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
                headers = {'Authorization': f'Bearer {st.session_state.access_token}'}
                data = {
                    'name': template_name,
                    'content': template_content
                }
                response = requests.post(f"{API_BASE}/templates", 
                                       headers=headers, json=data)
                
                if response.status_code == 200:
                    st.success(f"模板 '{template_name}' 上传成功！")
                    # Refresh template list
                    st.session_state.user_templates = get_user_templates(st.session_state.access_token)
                    st.rerun()
                else:
                    st.error(f"上传模板失败: {response.text}")
            except Exception as e:
                st.error(f"网络错误: {str(e)}")
    
    st.divider()
    
    # Manual template creation
    st.write("**方式二：手动创建模板**")
    with st.form("create_template_form"):
        new_template_name = st.text_input("模板名称")
        new_template_content = st.text_area("模板内容", height=200, 
            value="""# {{company_name}} 报告模板

## 公司概况
{{company_name}} 是一家专业的金融服务公司。

## 财务状况
### 资产负债表
- 总资产：{{total_assets}}
- 总负债：{{total_liabilities}}
- 净资产：{{net_assets}}

### 利润表
- 营业收入：{{revenue}}
- 净利润：{{net_profit}}

## 业务数据
### 放贷规模
- 累计放贷：{{total_loans}}
- 在贷余额：{{outstanding_balance}}

### 风险指标
- 不良率：{{npl_rate}}
- 逾期率：{{overdue_rate}}

## 总结
{{company_name}} 在 {{year}} 年度表现良好。
""")
        
        if st.form_submit_button("创建模板"):
            if new_template_name and new_template_content:
                # Create template via API
                try:
                    API_BASE = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
                    headers = {'Authorization': f'Bearer {st.session_state.access_token}'}
                    data = {
                        'name': new_template_name,
                        'content': new_template_content
                    }
                    response = requests.post(f"{API_BASE}/templates", 
                                           headers=headers, json=data)
                    
                    if response.status_code == 200:
                        st.success("模板创建成功！")
                        # Refresh template list
                        st.session_state.user_templates = get_user_templates(st.session_state.access_token)
                        st.rerun()
                    else:
                        st.error(f"创建模板失败: {response.text}")
                except Exception as e:
                    st.error(f"网络错误: {str(e)}")
            else:
                st.error("请输入模板名称和内容")
    
    st.divider()
    
    # List existing templates
    st.subheader("现有模板")
    if st.session_state.user_templates:
        for template in st.session_state.user_templates:
            col1, col2 = st.columns([3, 1])
            with col1:
                st.write(f"**{template['name']}**")
                st.caption(f"路径: {template['path']}")
            with col2:
                if st.button("删除", key=f"delete_{template['id']}"):
                    # Delete template via API
                    try:
                        API_BASE = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
                        headers = {'Authorization': f'Bearer {st.session_state.access_token}'}
                        response = requests.delete(f"{API_BASE}/templates/{template['id']}", 
                                                 headers=headers)
                        
                        if response.status_code == 200:
                            st.success(f"模板 '{template['name']}' 删除成功！")
                            # Refresh template list
                            st.session_state.user_templates = get_user_templates(st.session_state.access_token)
                            st.rerun()
                        else:
                            st.error(f"删除模板失败: {response.text}")
                    except Exception as e:
                        st.error(f"网络错误: {str(e)}")
    else:
        st.info("暂无模板")

@handle_layer_boundary(LayerType.PRESENTATION, "streamlit application initialization")
async def main():
    """Main Streamlit application with unified error handling"""
    set_operation_context("streamlit_app_init")
    set_layer_context("presentation")
    
    with error_boundary("environment setup", LayerType.PRESENTATION):
        try:
            # Load environment variables
            load_dotenv(override=True)
            
            # Debug environment variables
            logger.info("DEBUG - 初始化Streamlit前的环境变量:")
            logger.info(f"OPENAI_API_BASE: {os.getenv('OPENAI_API_BASE')}")
            logger.info(f"OPENAI_API_KEY: {'*' * len(os.getenv('OPENAI_API_KEY', ''))}")
            logger.info(f"MODEL_ID: {os.getenv('MODEL_ID')}")
            
        except Exception as e:
            raise ConfigurationException(
                message="Failed to initialize application environment",
                details=str(e),
                original_exception=e,
                suggested_action="Check environment variables and configuration files"
            )

    # Function to load and display cropped screenshot
    async def process_screenshot():
        """Async function to process screenshot data"""
        try:
            if not st.session_state.crawler_status.get('screen'):
                logger.warning("No screenshot data in crawler_status")
                return None
                
            # Decode base64 safely
            try:
                screenshot_data = base64.b64decode(st.session_state.crawler_status['screen'])
                if not screenshot_data:
                    logger.warning("Empty screenshot data after decoding")
                    return None
            except Exception as decode_error:
                logger.error(f"Base64 decode failed: {str(decode_error)}")
                return None

            # Use temp file to avoid conflicts
            temp_file = "screen.png"
            with open(temp_file, "wb") as f:
                f.write(screenshot_data)
                logger.debug(f"Saved screenshot to {temp_file}")

            try:
                img = Image.open(temp_file)
                width, height = img.size
                cropped_img = img.crop((0, 0, width, height//2))
                
                img_bytes = io.BytesIO()
                cropped_img.save(img_bytes, format='PNG')
                img_bytes.seek(0)
                
                # Clean up temp file
                os.remove(temp_file)
                return img_bytes
            except Exception as img_error:
                logger.error(f"Image processing failed: {str(img_error)}")
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                return None
        except Exception as e:
            logger.error(f"Error in process_screenshot: {str(e)}", exc_info=True)
            return None

    async def display_cropped_screenshot():
        """Async function to display screenshot in Streamlit UI"""
        try:
            img_bytes = await process_screenshot()
            
            if img_bytes:
                if 'image_container' not in st.session_state:
                    st.session_state.image_container = st.empty()
                
                st.session_state.image_container.image(
                    img_bytes,
                    caption=f"Crawler screenshot (top half) - Last refresh: {time.strftime('%H:%M:%S')}"
                )
                logger.debug("Displayed cropped screenshot successfully")
        except Exception as e:
            logger.error(f"Error displaying screenshot: {str(e)}", exc_info=True)

    # 优化的爬虫登录管理
    if 'crawler_status' not in st.session_state:
        logger.info("Initializing crawler session")
        with st.spinner("Initializing crawler..."):
            # 初始化时只检查状态，不执行登录
            st.session_state.crawler_status = await check_crawler_status()
            # 如果未登录，执行一次登录操作
            if not st.session_state.crawler_status.get('logged_in', False):
                logger.info("Performing initial login attempt")
                login_result = await perform_crawler_login()
                st.session_state.crawler_status.update(login_result)
            
            logger.info(f"Crawler initialized - logged_in: {st.session_state.crawler_status.get('logged_in', False)}")

    # 显示登录界面（仅在未登录时）
    if not st.session_state.crawler_status.get('logged_in', False):
        logger.info("Showing crawler login interface")
        try:
            # 创建登录界面容器
            login_container = st.container()
            with login_container:
                with st.expander("爬虫登录", expanded=True):
                    # 状态显示
                    status_col, refresh_col = st.columns([3, 1])
                    
                    with status_col:
                        status_placeholder = st.empty()
                        status_placeholder.info(f"状态: {st.session_state.crawler_status.get('message', '未知')}")
                    
                    with refresh_col:
                        if st.button("🔄 刷新状态", key="refresh_status"):
                            logger.info("Manual status refresh requested")
                            st.session_state.crawler_status = await check_crawler_status()
                            if st.session_state.crawler_status.get('logged_in', False):
                                st.success("登录成功！")
                                st.rerun()
                            else:
                                status_placeholder.info(f"状态: {st.session_state.crawler_status.get('message', '未知')}")
                    
                    # 二维码显示区域
                    if st.session_state.crawler_status.get('screen'):
                        if 'image_container' not in st.session_state:
                            st.session_state.image_container = st.empty()
                        
                        # 显示二维码
                        await display_cropped_screenshot()
                        
                        # 二维码刷新按钮
                        col1, col2 = st.columns([1, 1])
                        with col1:
                            if st.button("🔄 刷新二维码", key="refresh_qr"):
                                logger.info("Manual QR code refresh requested")
                                new_screen = await refresh_crawler_screenshot()
                                if new_screen:
                                    st.session_state.crawler_status['screen'] = new_screen
                                    await display_cropped_screenshot()
                                    st.success("二维码已刷新")
                                else:
                                    st.warning("二维码刷新失败")
                        
                        with col2:
                            if st.button("🚀 重新登录", key="retry_login"):
                                logger.info("Manual login retry requested")
                                with st.spinner("正在重新登录..."):
                                    login_result = await perform_crawler_login()
                                    st.session_state.crawler_status.update(login_result)
                                    if login_result.get('logged_in', False):
                                        st.success("登录成功！")
                                        st.rerun()
                                    else:
                                        st.warning("登录仍未完成，请扫描二维码")
                    
                    # 自动状态检查（降低频率）
                    if 'last_auto_check' not in st.session_state:
                        st.session_state.last_auto_check = time.time()
                    
                    # 每30秒自动检查一次状态
                    if time.time() - st.session_state.last_auto_check > 30:
                        st.session_state.last_auto_check = time.time()
                        logger.debug("Performing automatic status check")
                        status_result = await check_crawler_status()
                        if status_result.get('logged_in', False):
                            st.session_state.crawler_status.update(status_result)
                            logger.info("Auto-check detected login success")
                            st.success("登录成功！")
                            st.rerun()
                        else:
                            # 只更新状态信息，不更新截图
                            st.session_state.crawler_status.update({
                                'logged_in': status_result.get('logged_in', False),
                                'message': status_result.get('message', ''),
                                'status': status_result.get('status', '')
                            })
                            status_placeholder.info(f"状态: {st.session_state.crawler_status.get('message', '未知')}")
                    
                    st.info("💡 提示：请使用企查查APP扫描二维码完成登录，或点击上方按钮手动刷新状态")
                    
        except Exception as e:
            logger.error(f"Error in login interface: {str(e)}", exc_info=True)
            st.error(f"登录界面错误: {str(e)}")
            return

    async def init_agent(access_token):
        config = {
            "workspace_id": st.session_state.department
        }
        
        logger.debug(f"Initializing agent with config: {config}")
        
        # Create ClientConfig for ReportAgentClient
        from app.api.client import ClientConfig
        api_base_url = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
        client_config = ClientConfig(
            base_url=api_base_url,
            auth_token=access_token
        )
        
        report_client = ReportAgentClient(client_config)
        
        response = report_client.create_session(config)
        
        # 使用API返回数据初始化agent
        st.session_state.agent = type('Agent', (), {})()  # 创建动态对象
        agent = st.session_state.agent
        
        # 设置基本属性
        agent.config = AgentConfig(**config)
        agent.agent = None
        
          # 从agent_state设置核心字段
        agent_state = response["agent_state"]
        agent.session_id = response.get("session_id")
        agent.company_name = agent_state.get("company")
        agent.report_year = agent_state.get("year")
        agent.report_quarter = agent_state.get("quarter")
        agent.template = agent_state.get("template")
        agent.required_files = agent_state.get("required_files", {
            'collections': [],
            'confirmed_empty': False
        })
        
        # 添加必要的方法引用
        agent.sync_to_ui = lambda ui_state: ui_state.update({
            'company': agent.company_name,
            'year': agent.report_year,
            'quarter': agent.report_quarter,
            'template': agent.template
        })
        
        agent.reset = lambda: None  # 重置由API处理

        # Scan for existing reports via API
        try:
            scan_result = report_client.scan_reports(config["workspace_id"])
            if scan_result.get('status') == 'success':
                st.session_state.available_reports = scan_result['available_reports']
            else:
                logger.warning(f"Failed to scan reports: {scan_result.get('message', 'Unknown error')}")
                st.session_state.available_reports = []
        except Exception as e:
            logger.error(f"Error scanning reports: {str(e)}")
            st.session_state.available_reports = []
        
        return agent

    # Check authentication first
    if 'authenticated' not in st.session_state or not st.session_state.authenticated:
        show_login_page()
        return

    # Initialize session state only after authentication
    if 'agent' not in st.session_state:
        # 初始化配置并创建Agent (需要在登录后进行)
        st.session_state.agent = await init_agent(st.session_state.access_token)
        st.session_state.agent_session = st.session_state.agent.session_id
        logger.info(f"Initialized agent: {st.session_state.agent_session}")
        
        # 初始化时自动检测模板
        # from pathlib import Path
        # template_files = list(Path("templates").glob("*.md"))
        # auto_template = template_files[0].name if len(template_files) == 1 else None
        
        st.session_state.current_values = {
            'company': None,
            'year': None,
            'quarter': None,
            # 'template': auto_template,  # 自动设置唯一模板
            'template_file': None,
            'materials_count': 0  # 新增资料文件数量
        }
        st.session_state.uploaded_files = []  # 初始化上传文件列表
        
    # 初始化应用状态和文件管理
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    
    if 'uploader_key' not in st.session_state:
                st.session_state.uploader_key = 0

    # 实时同步文件状态到session
    if st.session_state.current_values.get('company') and st.session_state.current_values.get('year'):
        workspace_path = os.path.join(
            "workspaces", 
            st.session_state.agent.config.workspace_id,
            st.session_state.current_values['company'],
            str(st.session_state.current_values['year'])
        )
        os.makedirs(workspace_path, exist_ok=True)
        logger.debug(f"确保session工作输出目录存在: {workspace_path}")
        # 获取目录中所有有效文件
        existing_files = [
            f for f in os.listdir(workspace_path) 
            if f.lower().endswith(('.pdf','.xls','xlsx'))
        ]
        # 合并已上传文件和目录中已有文件（去重）
        current_files = st.session_state.agent.required_files['collections']
        st.session_state.agent.required_files['collections'] = list(
            set(current_files + existing_files)
        )
        # 更新文件计数
        st.session_state.current_values['materials_count'] = len(st.session_state.agent.required_files['collections'])
    else:
        if hasattr(st.session_state.agent, 'required_files'):
            st.session_state.agent.required_files['collections'] = []
        if hasattr(st.session_state, 'materials_count'):
            st.session_state.current_values['materials_count'] = 0
        
    # 初始化已处理文件集合
    if 'processed_files' not in st.session_state:
        st.session_state.processed_files = set()

    # 文件上传回调函数（支持多文件）
    def save_uploaded_file(uploaded_files):
        import os
        
        new_files = [f for f in uploaded_files if f.name not in st.session_state.processed_files]
        if not new_files:
            return
            
        logger.debug(f"开始处理文件上传，共{len(new_files)}个新文件")
        progress_bar = st.progress(0)
        
        # 遍历所有上传文件
        for i, uploaded_file in enumerate(new_files):
            progress = (i + 1) / len(new_files)
            progress_bar.progress(progress)
            
            workspace_path = os.path.join("workspaces", st.session_state.agent.config.workspace_id)
            company_dir = os.path.join(workspace_path, st.session_state.current_values['company'] or "未命名公司")
            year_dir = os.path.join(company_dir, str(st.session_state.current_values['year'] or "未指定年份"))
            
            logger.debug(f"创建工作输出区目录: {year_dir}")
            os.makedirs(year_dir, exist_ok=True)
            
            file_path = os.path.join(year_dir, uploaded_file.name)
            logger.debug(f"保存文件到: {file_path}")
            
            # 仅保存新文件（避免重复）
            if not os.path.exists(file_path):
                logger.debug(f"保存新文件: {uploaded_file.name}")
                with open(file_path, "wb") as f:
                    f.write(uploaded_file.getbuffer())
                
                # 去重后添加
                if uploaded_file.name not in st.session_state.agent.required_files['collections']:
                    st.session_state.agent.required_files['collections'].append(uploaded_file.name)
                
                # 标记为已处理
                st.session_state.processed_files.add(uploaded_file.name)
        
        # 完成上传后显示100%进度
        progress_bar.progress(1.0)
        st.session_state.processed_files.update(f.name for f in new_files)  # 标记已处理
        
        # 显示上传成功消息并更新UI
        st.success("文件上传成功!")
        # 重置上传组件状态
        st.session_state.uploaded_files = []
        st.session_state.uploader_key += 1  # 生成新的唯一key
        st.rerun()

    # 显示已上传资料文件（独立区域）
    with st.sidebar:
        if st.session_state.current_values['company'] and st.session_state.current_values['year']:
            # 显示已上传资料文件
            if st.session_state.agent.required_files.get('confirmed_empty', False):
                st.info("已确认无需额外资料")
            elif st.session_state.agent.required_files['collections']:
                st.subheader("已上传资料")
                for file in st.session_state.agent.required_files['collections']:
                    st.write(f"✅ {file}")
            else:
                st.info("尚未上传任何资料文件")

            # 文件管理标题            
            st.header("文件管理")
            workspace_path = f"workspaces/{st.session_state.agent.config.workspace_id}/" + \
                            f"{st.session_state.current_values['company']}/" + \
                            f"{st.session_state.current_values['year']}"
            st.caption(f"当前路径: {workspace_path}")
        
            uploaded_files = st.file_uploader("上传公司资料 (PDF/Excel)", 
                                            type=['pdf','xls','xlsx'],
                                            accept_multiple_files=True,
                                            label_visibility="visible",
                                            help="请拖放文件到此处或点击选择文件上传",
                                            key=f'file_uploader_{st.session_state.uploader_key}')
            if uploaded_files:
                save_uploaded_file(uploaded_files)            
        else:
            st.info("请先设置公司名称和年份以启用文件管理功能")

    # 使用Streamlit原生布局系统
    st.markdown(
        """
        <style>
            section[data-testid="stSidebar"] {
                width: 300px !important;
            }
            section.main > div {
                padding-left: 2rem;
                padding-right: 2rem;
                max-width: none !important;
            }
            div.stButton > button {
                width: 100%;
            }
            div.block-container {
                max-width: none !important;
                padding-left: 5rem;
                padding-right: 5rem;
            }
            /* Fix chat input to bottom with fixed width and centered */
            div.stChatInput[data-testid="stChatInput"] {
                width: 500px;
                margin: 0 auto;
                position: fixed;
                bottom: 1rem;
                left: 50%;
                transform: translateX(-50%);
            }
            /* Add padding to prevent content overlap */
            div[data-testid="stVerticalBlock"] > div:has(> div[data-testid="stHorizontalBlock"]) ~ div {
                padding-bottom: 5rem;
            }
        </style>
        """,
        unsafe_allow_html=True,
    )
    
    # 主布局
    main_col = st.container()
    with main_col:
        col1, col2 = st.columns([0.8, 0.2], gap="medium")

    with col2:
        with st.container():
            st.header("模板管理")
            
            # Load user templates if not already loaded
            if 'user_templates' not in st.session_state:
                st.session_state.user_templates = get_user_templates(st.session_state.access_token)
            
            # Create template options
            template_options = []
            if st.session_state.user_templates:
                template_options = [template['name'] for template in st.session_state.user_templates]
            if template_options:
                template_options = set(template_options)
            
            
            selected_template = st.selectbox(
                "选择模板",
                options=template_options,
                help="选择要使用的报告模板"
            )
            
            if selected_template:
                # Find the template file path
                template_file = f"{selected_template}.md"
                for template in st.session_state.user_templates:
                    if template['name'] == selected_template:
                        template_file = template['path']
                        break
                
                st.session_state.current_values['template_file'] = template_file
                st.session_state.agent.template = template_file
            
            # Template management buttons
            col_a, col_b = st.columns(2)
            with col_a:
                if st.button("刷新模板", help="重新加载模板列表"):
                    st.session_state.user_templates = get_user_templates(st.session_state.access_token)
                    st.rerun()
            
            with col_b:
                if st.button("模板管理", help="打开模板管理界面"):
                    st.session_state.show_template_manager = True
                    st.rerun()
            
            # 报告下载区域
            st.divider()
            st.subheader("报告下载")
            
            # 所有可用的历史报告
            if hasattr(st.session_state, 'available_reports') and st.session_state.available_reports:
                for report in st.session_state.available_reports:
                    with open(report['path'], "rb") as f:
                        st.download_button(
                            label=f"{report['company']} {report['year']}",
                            data=f,
                            file_name=f"{report['company']}_report.docx",
                            mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                            key=f"report_{report['company']}_{report['year']}"
                        )
            else:
                st.info("没有可用的历史报告")

    with col1:
        st.title("智能报告生成系统")
        
        # 显示四要素状态
        with st.expander("当前要素状态", expanded=True):
            cols = st.columns(5)
            cols[0].metric("公司名称", st.session_state.current_values['company'] or "未设置", 
                        help=st.session_state.current_values['company'] if st.session_state.current_values['company'] else None)
            cols[1].metric("年份", st.session_state.current_values['year'] or "未设置")
            cols[2].metric("季度", st.session_state.current_values.get('quarter') or "未设置")
            # Extract template name from file path for display
            template_display = "未设置"
            if st.session_state.current_values.get('template_file'):
                template_path = st.session_state.current_values['template_file']
                # Extract filename from path
                template_display = os.path.basename(template_path)
            
            cols[3].metric("模板", template_display,
                        help=f"文件: {st.session_state.current_values['template_file']}" if st.session_state.current_values.get('template_file') else None)
            cols[4].metric("资料文件", 
                        f"{st.session_state.current_values['materials_count']}个")

        # 创建聊天容器并显示历史消息
        chat_container = st.container()
        with chat_container:
            for msg in st.session_state.chat_history:
                with st.chat_message(msg["role"]):
                    st.write(msg["content"])

        # 处理用户输入
        if user_input := st.chat_input("请输入信息或回复确认请求"):
                # 移除礼貌性前缀
                user_input = user_input.lstrip("请麻烦烦请劳帮忙请问能否可以帮我能不能可不可以")
                
                # 立即显示用户消息
                with chat_container:
                    with st.chat_message("user"):
                        st.write(user_input)
                
                # 然后添加到历史记录
                st.session_state.chat_history.append({
                    "role": "user",
                    "content": user_input
                })
                logger.debug(f"收到用户输入: {user_input}")
                
                if user_input.strip().lower() in ["clear", "修改"]:
                    session_state_reset()
                    st.write("已重置，请输入新要求")
                    st.rerun()
                    return
                
                if user_input.strip().lower() == "reset crawler":
                    logger.info("收到重置爬虫请求")
                    try:
                        client = CrawlerClient()
                        await client.reset()
                        logger.info("爬虫重置成功")
                    except Exception as e:
                        logger.error(f"爬虫重置失败: {str(e)}")
                    finally:
                        del st.session_state.crawler_status
                        with chat_container:
                            with st.chat_message('assistant'):
                                st.write("爬虫状态已重置，正在重新初始化...")
                        st.session_state.chat_history.append({
                            "role": "assistant",
                            "content": "爬虫状态已重置，请重试"
                        })
                        st.rerun()
                
                # 添加tab信息查询功能
                if user_input.strip().lower() in ["tabs", "tab info", "tab状态", "标签页"]:
                    logger.info("收到tab信息查询请求")
                    try:
                        client = CrawlerClient()
                        tab_info = await client.get_tab_info()
                        
                        # 格式化tab信息显示
                        tab_message = f"""📊 **浏览器标签页信息**

🔢 **当前标签页数量**: {tab_info.get('current_tabs', 0)}
📏 **最大允许数量**: {tab_info.get('max_tabs', 0)}
📈 **使用率**: {tab_info.get('usage_percentage', 0)}%
🔗 **浏览器连接状态**: {'已连接' if tab_info.get('browser_connected', False) else '未连接'}

{'⚠️ **警告**: 标签页数量超过限制，建议重置' if tab_info.get('needs_reset', False) else '✅ **状态**: 标签页使用正常'}"""
                        
                        with chat_container:
                            with st.chat_message('assistant'):
                                st.markdown(tab_message)
                        st.session_state.chat_history.append({
                            "role": "assistant",
                            "content": tab_message
                        })
                        await client.close()
                        logger.info("tab信息查询成功")
                    except Exception as e:
                        logger.error(f"tab信息查询失败: {str(e)}")
                        error_message = f"无法获取标签页信息: {str(e)}"
                        with chat_container:
                            with st.chat_message('assistant'):
                                st.write(error_message)
                        st.session_state.chat_history.append({
                            "role": "assistant",
                            "content": error_message
                        })
                    st.rerun()
                # 检查要素完整性
                is_complete, missing = check_elements_complete(
                    st.session_state.current_values,
                    st.session_state.agent
                )
                logger.debug(f"当前要素: 完备：{is_complete}，缺失：{missing}")
                    # 要素齐全时处理确认流程
                if confirm_generation(user_input):
                    if is_complete:
                        try:
                            response = await execute_rag_workflow()
                            with chat_container:
                                with st.chat_message('assistant'):
                                    st.write(response['message'])
                            st.session_state.chat_history.append({
                                "role": "assistant",
                                "content": response['message']
                            })
                            st.rerun()
                            return
                        except Exception as e:
                            logger.error(f"执行RAG工作流时出错: {str(e)}", exc_info=True)
                            with chat_container:
                                with st.chat_message('assistant'):
                                    st.write(f"⚠️ 系统处理您的请求时遇到问题\n\n可能原因:\n- 输入格式不符合要求\n- 网络连接问题\n- 系统临时故障\n\n建议操作:\n1. 检查输入格式\n2. 稍后输入“继续”再试\n3. 如问题持续,请联系管理员并提供错误时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
                            st.session_state.chat_history.append({
                                "role": "assistant",
                                "content": f"⚠️ 系统处理您的请求时遇到问题\n\n可能原因:\n- 输入格式不符合要求\n- 网络连接问题\n- 系统临时故障\n\n建议操作:\n1. 检查输入格式\n2. 稍后输入“继续”再试\n3. 如问题持续,请联系管理员并提供错误时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
                            })
                            st.rerun()
                            
                    
                # 用户选择修改
                from app.api.client import ClientConfig
                api_base_url = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
                client_config = ClientConfig(
                    base_url=api_base_url,
                    auth_token=st.session_state.access_token
                )
                
                report_client = ReportAgentClient(client_config)

                try:
                    # Verify session_id exists
                    if not hasattr(st.session_state, 'agent_session') or not st.session_state.agent_session:
                        logger.error("Missing agent session ID")
                        st.error("会话已过期，请刷新页面重新登录")
                        return
                    
                    # Ensure we pass valid strings, not None values
                    current_company = st.session_state.current_values.get('company', '') or ''
                    current_year = st.session_state.current_values.get('year', '') or ''
                    current_quarter = st.session_state.current_values.get('quarter', '') or ''
                    
                    # Convert "未设置" to empty string for API
                    if current_company == '未设置':
                        current_company = ''
                    if current_year == '未设置':
                        current_year = ''
                    if current_quarter == '未设置':
                        current_quarter = ''
                    
                    logger.debug(f"Sending to API - session_id: {st.session_state.agent_session}, company: '{current_company}', year: '{current_year}', quarter: '{current_quarter}'")
                    response = report_client.process(
                        st.session_state.agent_session,  # session_id as first positional argument
                        user_input,                      # user_input as second positional argument
                        company=current_company,
                        year=current_year,
                        quarter=current_quarter,
                        extra_context={
                            'conversation_history': st.session_state.chat_history[-5:],
                            'ui_state': {
                                **st.session_state.current_values,
                                'company_options': getattr(st.session_state.agent, 'company_options', None),
                                'conversation_phase': getattr(st.session_state.agent, 'conversation_phase', 'initial')
                            }
                        }
                    )
                    logger.debug(f"API response: {response}")
                    
                    if response.get('status') == 'error':
                        error_code = response.get('code', 'ERR999')
                        error_message = response.get('message', '未知错误')
                        logger.error(f"API returned error: {error_code} - {error_message}")
                        st.session_state.chat_history.append({
                            "role": "assistant",
                            "content": f"⚠️ 系统处理您的请求时遇到问题\n\n可能原因:\n- 输入格式不符合要求\n- 网络连接问题\n- 系统临时故障\n\n建议操作:\n1. 检查输入格式\n2. 稍后输入“继续”再试\n3. 如问题持续,请联系管理员并提供错误时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
                        })
                        st.error(f"⚠️ 系统处理您的请求时遇到问题\n\n可能原因:\n- 输入格式不符合要求\n- 网络连接问题\n- 系统临时故障\n\n建议操作:\n1. 检查输入格式\n2. 稍后输入“继续”再试\n3. 如问题持续,请联系管理员并提供错误时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
                        st.rerun()
                        return
                    
                    # Handle successful response
                    # ... existing success handling code ...
                    
                except Exception as e:
                    logger.error(f"Unexpected error calling report_client.process: {str(e)}", exc_info=True)
                    st.error("❌ An unexpected error occurred while processing your request")
                    st.caption(f"Error: {str(e)}")
                    st.info("💡 Please try again or contact support if the problem persists")
                    return

                # 使用API返回的agent_state更新session
                agent = st.session_state.agent
                agent_state = response["agent_state"]
                agent.company_name = agent_state["company"]
                agent.report_year = agent_state["year"]
                agent.template = agent_state["template"]
                agent.required_files = agent_state["required_files"]
                
                # 同步到UI状态
                st.session_state.current_values.update({
                    'company': agent_state["company"],
                    'year': agent_state["year"],
                    'quarter': agent_state.get("quarter"),
                    'template_file': agent_state["template"]
                })
                
                # 调试日志
                logger.debug(
                    f"状态同步完成 - UI: {st.session_state.current_values}\n"
                )
                
                # 自动检查四要素完整性
                if all(st.session_state.current_values.values()) and \
                (st.session_state.agent.required_files['collections'] or 
                    st.session_state.agent.required_files['confirmed_empty']):
                    
                    st.session_state.confirmation_pending = True
                    st.session_state.pending_action = "execute_report"
                    
                    workspace_path = os.path.join(
                        "workspaces",
                        st.session_state.agent.config.workspace_id,
                        st.session_state.current_values['company'],
                        str(st.session_state.current_values['year'])
                    )
                    files = [f for f in Path(workspace_path).glob("*")]
                    file_list = "暂无" if not files else "".join(f"　　📎 {f.name}\n\n" for f in files if f.is_file())
                    # 准备季度显示信息
                    quarter_info = f"📊 **报告季度**: {st.session_state.current_values.get('quarter')}\n" if st.session_state.current_values.get('quarter') else ""
                    
                    response['message'] = f"""✅ 所有要素已完备！请确认以下信息：\n
🏢 **公司名称**: {st.session_state.current_values['company']}\n
📅 **报告年份**: {st.session_state.current_values['year']}\n
{quarter_info}
📂 **已上传资料**:\n
{file_list}\n
请回复「是」开始生成报告，或「否」修改信息"""
            
                with st.chat_message('assistant'):
                    st.markdown(response['message'])

                # 检查是否需要开始生成报告
                if response.get('message') == "开始生成报告...":
                    response = await execute_rag_workflow()

                # 添加助理响应到历史记录
                st.session_state.chat_history.append({
                    "role": "assistant",
                    "content": response['message']
                })
                
                logger.debug("界面强制刷新")
                st.rerun()



if __name__ == "__main__":
    # Debug config after initialization
    logger.info("\nDEBUG - Config after initialization:")
    config = AgentConfig(workspace_id='debug')
    logger.info(f"API Base: {config.api_base}")
    logger.info(f"Model: {config.model_name}")
    
    try:
        asyncio.run(main())
    except Exception as e:
        logger.error(f"Application failed: {str(e)}")
        raise
