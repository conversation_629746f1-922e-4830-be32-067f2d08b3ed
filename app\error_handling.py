"""
Cross-Layer Exception Handling Framework

This module provides utilities and decorators for implementing the unified
error handling strategy across application layers. It enforces the following
principles:

1. Boundary Exception Management: Proper wrapping and logging at layer boundaries
2. Exception Flow Control Prohibition: Prevents using exceptions for control flow
3. Intra-Layer Exception Propagation: Transparent propagation within layers
4. Exception Wrapping Requirements: Maintains stack traces with meaningful context
5. Try-Catch Minimization: Only use when adding value

Usage Examples:

    # At layer boundaries (e.g., API calling business logic)
    @handle_layer_boundary(LayerType.PRESENTATION, "user authentication")
    def authenticate_user(username, password):
        return auth_service.authenticate(username, password)
    
    # Within same layer for logging context
    @log_and_reraise(logger, "processing user data")
    def process_data(data):
        return transform_data(data)
    
    # Manual exception wrapping
    try:
        result = business_logic_function()
    except Exception as e:
        raise wrap_layer_boundary_exception(
            e, LayerType.PRESENTATION, "user operation", logger
        )
"""

import logging
import functools
from typing import Optional, Dict, Any, Callable, TypeVar, Union
from contextlib import contextmanager

from app.exceptions import (
    BaseAppException, LayerType, ErrorSeverity,
    PresentationLayerException, BusinessLogicException, UtilityException,
    wrap_layer_boundary_exception
)
from app.logging_config import set_layer_context, set_operation_context

# Type variable for generic function decoration
F = TypeVar('F', bound=Callable[..., Any])


class ErrorHandlingContext:
    """
    Context manager for error handling operations.

    Provides a clean way to handle exceptions with proper logging and context
    without requiring decorators.
    """

    def __init__(
        self,
        operation_context: str,
        layer: LayerType,
        logger: Optional[logging.Logger] = None,
        additional_context: Optional[Dict[str, Any]] = None,
        wrap_exceptions: bool = True
    ):
        self.operation_context = operation_context
        self.layer = layer
        self.logger = logger or logging.getLogger(__name__)
        self.additional_context = additional_context or {}
        self.wrap_exceptions = wrap_exceptions

    def add_context(self, key: str, value: Any) -> None:
        """
        Add context information to be included in any exceptions.

        Args:
            key: Context key
            value: Context value
        """
        self.additional_context[key] = value

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            return False

        if self.wrap_exceptions and not isinstance(exc_val, BaseAppException):
            # Wrap the exception with layer context
            wrapped = wrap_layer_boundary_exception(
                exc_val, self.layer, self.operation_context,
                self.logger, self.additional_context
            )
            # Replace the exception - don't log here since it will be logged by exception handler
            raise wrapped from exc_val
        elif isinstance(exc_val, BaseAppException):
            # Add context to existing exception but don't log here to avoid duplication
            exc_val.context.update(self.additional_context)
            # Only log at DEBUG level to avoid spam
            self.logger.debug(f"Exception context updated in {self.operation_context}: {exc_val.error_code}")

        return False


def handle_layer_boundary(
    target_layer: LayerType,
    operation_context: str,
    logger: Optional[logging.Logger] = None,
    additional_context: Optional[Dict[str, Any]] = None
) -> Callable[[F], F]:
    """
    Decorator for handling exceptions at layer boundaries.
    
    This decorator should be applied to functions that represent layer boundaries
    to ensure proper exception wrapping and logging.
    
    Args:
        target_layer: The layer that this function belongs to
        operation_context: Description of the operation for error context
        logger: Logger instance (will create default if not provided)
        additional_context: Additional context to include in exceptions
        
    Returns:
        Decorated function with boundary exception handling
        
    Usage:
        @handle_layer_boundary(LayerType.PRESENTATION, "user authentication")
        def authenticate_user(username, password):
            # This function calls business logic layer
            return auth_service.authenticate(username, password)
    """
    def decorator(func: F) -> F:
        nonlocal logger
        if logger is None:
            logger = logging.getLogger(func.__module__)
        
        context = additional_context or {}
        context.update({
            'function_name': func.__name__,
            'function_module': func.__module__,
            'target_layer': target_layer.value,
            'operation_context': operation_context
        })
        
        import inspect
        
        # Check if this is an async function
        if inspect.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                # Update context with call-specific info
                call_context = context.copy()
                call_context.update({
                    'args_count': len(args),
                    'kwargs_keys': list(kwargs.keys())
                })
                
                try:
                    return await func(*args, **kwargs)
                except BaseAppException as e:
                    # If it's already our exception type and from the same layer, re-raise
                    if e.layer == target_layer:
                        # Add additional context and re-log
                        e.context.update(call_context)
                        e.log_error(logger)
                        raise
                    # Otherwise, wrap it for the target layer
                    raise wrap_layer_boundary_exception(
                        e, target_layer, operation_context, logger, call_context
                    )
                except Exception as e:
                    # Wrap any other exception
                    raise wrap_layer_boundary_exception(
                        e, target_layer, operation_context, logger, call_context
                    )
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                # Update context with call-specific info
                call_context = context.copy()
                call_context.update({
                    'args_count': len(args),
                    'kwargs_keys': list(kwargs.keys())
                })
                
                try:
                    return func(*args, **kwargs)
                except BaseAppException as e:
                    # If it's already our exception type and from the same layer, re-raise
                    if e.layer == target_layer:
                        # Add additional context and re-log
                        e.context.update(call_context)
                        e.log_error(logger)
                        raise
                    # Otherwise, wrap it for the target layer
                    raise wrap_layer_boundary_exception(
                        e, target_layer, operation_context, logger, call_context
                    )
                except Exception as e:
                    # Wrap any other exception
                    raise wrap_layer_boundary_exception(
                        e, target_layer, operation_context, logger, call_context
                    )
            return sync_wrapper
    return decorator


def log_and_reraise(
    logger: logging.Logger,
    operation_context: str,
    additional_context: Optional[Dict[str, Any]] = None,
    severity_override: Optional[ErrorSeverity] = None
) -> Callable[[F], F]:
    """
    Decorator for logging exceptions before re-raising them.
    
    Use this decorator when you want to add logging context but don't need
    to wrap the exception (i.e., within the same layer).
    
    Args:
        logger: Logger instance for recording the exception
        operation_context: Description of the operation for logging context
        additional_context: Additional context information
        severity_override: Override severity level for logging
        
    Returns:
        Decorated function with enhanced logging
        
    Usage:
        @log_and_reraise(logger, "processing user data")
        def process_data(data):
            # Function that might raise exceptions
            return transform_data(data)
    """
    def decorator(func: F) -> F:
        context = additional_context or {}
        context.update({
            'operation_context': operation_context,
            'function_name': func.__name__,
            'function_module': func.__module__
        })
        
        import inspect
        
        # Check if this is an async function
        if inspect.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                # Update context with call-specific info
                call_context = context.copy()
                call_context.update({
                    'args_count': len(args),
                    'kwargs_keys': list(kwargs.keys())
                })
                
                try:
                    return await func(*args, **kwargs)
                except BaseAppException as e:
                    # Add additional context and re-log
                    e.context.update(call_context)
                    if severity_override:
                        e.severity = severity_override
                    e.log_error(logger)
                    raise
                except Exception as e:
                    # Log the raw exception with context
                    log_level = logging.ERROR
                    if severity_override == ErrorSeverity.CRITICAL:
                        log_level = logging.CRITICAL
                    elif severity_override == ErrorSeverity.LOW:
                        log_level = logging.WARNING
                    
                    logger.log(
                        log_level,
                        f"Exception in {operation_context}: {str(e)}",
                        extra=call_context,
                        exc_info=True
                    )
                    raise
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                # Update context with call-specific info
                call_context = context.copy()
                call_context.update({
                    'args_count': len(args),
                    'kwargs_keys': list(kwargs.keys())
                })
                
                try:
                    return func(*args, **kwargs)
                except BaseAppException as e:
                    # Add additional context and re-log
                    e.context.update(call_context)
                    if severity_override:
                        e.severity = severity_override
                    e.log_error(logger)
                    raise
                except Exception as e:
                    # Log the raw exception with context
                    log_level = logging.ERROR
                    if severity_override == ErrorSeverity.CRITICAL:
                        log_level = logging.CRITICAL
                    elif severity_override == ErrorSeverity.LOW:
                        log_level = logging.WARNING
                    
                    logger.log(
                        log_level,
                        f"Exception in {operation_context}: {str(e)}",
                        extra=call_context,
                        exc_info=True
                    )
                    raise
            return sync_wrapper
    return decorator


@contextmanager
def error_boundary(
    operation_context: str,
    layer: LayerType,
    logger: Optional[logging.Logger] = None,
    wrap_exceptions: bool = True,
    additional_context: Optional[Dict[str, Any]] = None
):
    """
    Context manager for creating error boundaries in code.

    This provides a more flexible alternative to decorators for handling
    exceptions in specific code blocks.

    Args:
        operation_context: Description of the operation
        layer: The layer this operation belongs to
        logger: Logger instance
        wrap_exceptions: Whether to wrap exceptions for layer boundaries
        additional_context: Additional context information

    Usage:
        with error_boundary("user data processing", LayerType.BUSINESS_LOGIC) as ctx:
            ctx.add_context("user_id", user_id)
            result = process_user_data(data)
            validate_result(result)
            return result
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    # Create an ErrorHandlingContext instance to yield
    context = ErrorHandlingContext(
        operation_context=operation_context,
        layer=layer,
        logger=logger,
        additional_context=additional_context,
        wrap_exceptions=wrap_exceptions
    )

    try:
        yield context
    except BaseAppException as e:
        # Add context to existing exception
        if context.additional_context:
            e.context.update(context.additional_context)
        e.log_error(logger)
        raise
    except Exception as e:
        if wrap_exceptions:
            # Wrap the exception with layer context
            wrapped = wrap_layer_boundary_exception(
                e, layer, operation_context, logger, context.additional_context
            )
            raise wrapped from e
        else:
            # Just log and re-raise
            ctx = context.additional_context or {}
            ctx.update({
                'operation_context': operation_context,
                'layer': layer.value,
                'exception_type': type(e).__name__
            })
            logger.error(
                f"Exception in {operation_context}: {str(e)}",
                extra=ctx,
                exc_info=True
            )
            raise


def safe_execute(
    func: Callable,
    *args,
    operation_context: str,
    layer: LayerType,
    logger: Optional[logging.Logger] = None,
    default_return: Any = None,
    **kwargs
) -> Any:
    """
    Safely execute a function with proper error handling.
    
    This function provides a way to execute operations with built-in error
    handling and optional default return values.
    
    Args:
        func: Function to execute
        *args: Positional arguments for the function
        operation_context: Description of the operation
        layer: The layer this operation belongs to
        logger: Logger instance
        default_return: Default value to return on exception
        **kwargs: Keyword arguments for the function
        
    Returns:
        Function result or default_return on exception
        
    Usage:
        result = safe_execute(
            risky_function, arg1, arg2,
            operation_context="data processing",
            layer=LayerType.UTILITY,
            default_return={}
        )
    """
    if logger is None:
        logger = logging.getLogger(__name__)
    
    try:
        return func(*args, **kwargs)
    except BaseAppException as e:
        e.log_error(logger)
        if default_return is not None:
            return default_return
        raise
    except Exception as e:
        wrapped = wrap_layer_boundary_exception(
            e, layer, operation_context, logger,
            {'function_name': func.__name__ if hasattr(func, '__name__') else str(func)}
        )
        if default_return is not None:
            return default_return
        raise wrapped from e
