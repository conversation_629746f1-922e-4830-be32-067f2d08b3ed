import json
import logging
import async<PERSON>
import os
from pathlib import Path
import re
import time
from typing import Callable, TypedDict, Optional, Dict, Any, Literal
from functools import wraps
import httpx
from langgraph.graph import StateGraph, END
from langchain_core.messages import HumanMessage
from app.utils.json_tools import extract_last_valid_json
# Legacy import removed - using unified error handling
from crawler.client import CrawlerClient

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, WorkflowException,
    ValidationException, ExternalServiceException, DataProcessingException,
    ConfigurationException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for workflow operations
set_layer_context("business_logic")

# Report workflow specific error codes
REPORT_ERROR_CODES = {
    'COMPANY_VALIDATION_FAILED': 'RPT_VAL_001',
    'CRAWLER_ERROR': 'RPT_CRAWL_001',
    'API_ERROR': 'RPT_API_001',
    'PROCESSING_ERROR': 'RPT_PROC_001'
}

class AgentState(TypedDict):
    company_name: Optional[str]
    report_year: Optional[str]
    report_quarter: Optional[str]  # 新增季度字段，值为"Q1","Q2","Q3","Q4"
    # template_name: Optional[str]  # User input name
    # template_file: Optional[str]  # Actual matched template filename
    materials_status: Literal['missing', 'confirmed_empty', 'exists']
    last_message: Optional[str]
    intent: Optional[Literal['element_processing', 'confirmation', 'other', 'company_selection']]
    missing_elements: Optional[Dict[str, str]]
    progress_callback: Optional[Callable[[str, int], None]]  # Callback for progress updates
    company_options: Optional[list[str]]  # 公司选项列表
    collected_elements: Dict[str, bool]  # 要素收集状态
    conversation_phase: Literal['initial', 'collecting', 'verifying', 'confirming']
    conversation_history: list[str]  # 最近5条对话记录


def create_report_workflow(agent):
    # 1. 意图分析节点
    @log_and_reraise(logger, "intent analysis")
    def intent_analysis(state: AgentState):
        """分析用户输入意图 with unified error handling"""
        set_operation_context("intent_analysis")
        logger.info("=== 意图分析开始 ===")

        with error_boundary("user input validation", LayerType.BUSINESS_LOGIC):
            try:
                user_input = state['last_message']
                if not user_input:
                    raise ValidationException(
                        message="Empty user input",
                        field_name="last_message",
                        details="User message cannot be empty",
                        suggested_action="Please provide a valid input message"
                    )
            except KeyError:
                raise ValidationException(
                    message="Missing user input in state",
                    field_name="last_message",
                    details="Required 'last_message' field not found in state",
                    context={'available_keys': list(state.keys())},
                    suggested_action="Ensure user input is properly captured"
                )
        digits = re.findall(r'\d+', user_input)  # Extract all digit sequences
            
        logger.debug(f"用户输入: {user_input}, 提取数字: {digits}")
        
        # 1. 特殊处理公司选择场景 - 优先检查
        if state.get('company_options'):
            logger.debug(f"检查公司选择: 输入='{user_input}', 选项={state['company_options']}")
            # 确保company_options同步到agent实例
            agent.company_options = state['company_options']
            
            # 精确匹配: 先检查是否完全匹配选项或序号
            if user_input in state['company_options']:
                state['intent'] = 'company_crawl'
                state['company_name'] = user_input
                agent.company_name = user_input
                state['company_options'] = None  # 清空候选
                logger.info(f"精确匹配公司选择: {user_input}")
                logger.info("=== 意图分析完成 ===")
                return state
                
            # 序号匹配: 检查是否输入的是选项序号
            try:
                index = int(digits[0]) - 1 if digits else ['0']
                if 0 <= index < len(state['company_options']):
                    selected_company = state['company_options'][index]  # 先保存选择的公司
                    state['intent'] = 'company_crawl'
                    state['company_name'] = selected_company
                    agent.company_name = selected_company
                    state['company_options'] = None  # 清空候选
                    logger.info(f"序号匹配公司选择: {user_input} -> {selected_company}")
                    logger.info("=== 意图分析完成 ===")
                    return state
            except Exception:
                pass

        # 更新对话历史
        state['conversation_history'] = (state.get('conversation_history', []) + [user_input])[-5:]
        logger.debug(f"现有state：{state}")

        # Create new state without overwriting quarter with conversation_history
        new_state = {
            'intent': 'element_processing',
            'modified_fields': [],
            'confidence': 0.9
        }
        # Preserve existing quarter value only if it's a valid string
        if 'report_quarter' in state and isinstance(state['report_quarter'], str):
            new_state['report_quarter'] = state['report_quarter']
        else:
            new_state['report_quarter'] = None
            
        # Copy other state properties except conversation_history
        for key in state:
            if key not in ['conversation_history', 'report_quarter']:
                new_state[key] = state[key]
        # 2. 提取公司名称 - 匹配2个以上中文字符
        company_match = re.search(r'([\u4e00-\u9fa5]{2,})', user_input)
        if company_match:
            new_state = {
                **new_state,
                'company_name': company_match.group(1),
                'modified_fields': ['company']
            }
        
        # 3. 增强版年份和季度提取 - 匹配4位数字或2位数字+年，以及可选的季度信息
        logger.info("开始提取年份和季度信息")
        
        # 先尝试匹配完整的年份+季度模式 (包括"2025年1季度"这种格式)
        period_match = re.search(r'(20\d{2}|(\d{2}))(?:[全]?[年]?[第]?([一二三四1-4])季[度]?|[Qq]([1-4]))?', user_input)
        logger.debug(f"年份+季度匹配结果: {period_match.groups() if period_match else None}")
        
        # 如果没有匹配到完整模式，尝试单独匹配季度
        quarter_only_match = None
        if not period_match or (not period_match.group(3) and not period_match.group(4)):
            quarter_only_match = re.search(r'[第]?([一二三四1-4])季[度]?|[Qq]([1-4])|(\d)[季]', user_input)
            logger.debug(f"单独季度匹配结果: {quarter_only_match.groups() if quarter_only_match else None}")
        
        extracted_year = None
        extracted_quarter = None
        
        # 处理年份+季度匹配结果
        if period_match:
            # 年份处理
            if '年' in period_match.group(1):  # 处理2位数字+年格式
                two_digit_year = period_match.group(2)
                full_year = f"20{two_digit_year}"  # 转换为4位年份
                if 2000 <= int(full_year) <= 2099:  # 验证年份范围
                    extracted_year = full_year
                    logger.debug(f"从2位数字+年格式提取年份: {extracted_year}")
            else:  # 处理4位数字格式
                if 2000 <= int(period_match.group(1)) <= 2099:
                    extracted_year = period_match.group(1)
                    logger.debug(f"从4位数字格式提取年份: {extracted_year}")
            
            # 季度处理
            quarter_group = period_match.group(3) or period_match.group(4)
            if quarter_group:
                # 将中文季度或数字转换为Q格式
                quarter_map = {'一': 'Q1', '二': 'Q2', '三': 'Q3', '四': 'Q4', 
                              '1': 'Q1', '2': 'Q2', '3': 'Q3', '4': 'Q4'}
                if quarter_group.startswith('Q'):
                    extracted_quarter = quarter_group.upper()
                    logger.debug(f"从Q格式提取季度: {extracted_quarter}")
                else:
                    extracted_quarter = quarter_map.get(quarter_group, None)
                    logger.debug(f"从中文/数字格式提取季度: {quarter_group} -> {extracted_quarter}")
        
        # 处理单独的季度匹配
        if quarter_only_match and not extracted_quarter:
            logger.debug(f"处理单独的季度匹配: {quarter_only_match.groups()}")
            if quarter_only_match.group(1):  # 中文季度
                quarter_map = {'一': 'Q1', '二': 'Q2', '三': 'Q3', '四': 'Q4'}
                extracted_quarter = quarter_map.get(quarter_only_match.group(1), None)
                logger.debug(f"从中文季度提取: {quarter_only_match.group(1)} -> {extracted_quarter}")
            elif quarter_only_match.group(2):  # Q格式季度
                extracted_quarter = f"Q{quarter_only_match.group(2)}"
                logger.debug(f"从Q格式季度提取: Q{quarter_only_match.group(2)} -> {extracted_quarter}")
            elif quarter_only_match.group(3):  # 数字季度 (如"1季")
                quarter_map = {'1': 'Q1', '2': 'Q2', '3': 'Q3', '4': 'Q4'}
                extracted_quarter = quarter_map.get(quarter_only_match.group(3), None)
                logger.debug(f"从数字季度提取: {quarter_only_match.group(3)} -> {extracted_quarter}")
        
        # 更新状态
        if extracted_year:
            new_state_update = {'report_year': extracted_year}
            logger.info(f"更新年份: {extracted_year}")
            if extracted_quarter:
                new_state_update['report_quarter'] = extracted_quarter
                # 同步到agent对象
                agent.report_quarter = extracted_quarter
                logger.info(f"更新季度: {extracted_quarter} (与年份一起)")
            
            new_state = {
                **new_state,
                **new_state_update
            }
            new_state['modified_fields'].extend(['year'])
            logger.debug(f"更新后的state: {new_state}")
            
        # 如果只有季度没有年份，也更新季度
        elif extracted_quarter:
            # Ensure we're setting a string value, not a dictionary
            if isinstance(extracted_quarter, str):
                new_state['report_quarter'] = extracted_quarter
                # 同步到agent对象
                agent.report_quarter = extracted_quarter
                logger.info(f"仅更新季度: {extracted_quarter} (无年份)")
            else:
                logger.warning(f"Invalid quarter type: {type(extracted_quarter)}, value: {extracted_quarter}")
            logger.debug(f"更新后的state: {new_state}")

        # 分析对话阶段特征
        phase_features = {
            'initial': "初次交互，期待获取基本信息",
            'collecting': "正在收集报告要素",
            'verifying': "验证信息准确性",
            'confirming': "等待最终确认"
        }.get(state.get('conversation_phase', 'initial'), "")
        
        # 提取历史对话中的关键要素变更
        prev_values = {
            'company': None,
            'year': None,
            'quarter': None,
            # 'template': None,
            'materials': None
        }
        
        # 分析历史对话中的要素变化
        for msg in state['conversation_history'][:-1]:
            if "公司名称" in msg or "公司" in msg:
                prev_values['company'] = msg
            elif "报告年份" in msg or "年份" in msg:
                prev_values['year'] = msg
            elif "季度" in msg:
                prev_values['quarter'] = msg
            # elif "模板" in msg or "报告类型" in msg:
            #     prev_values['template'] = msg
            elif "资料" in msg or "文件" in msg:
                prev_values['materials'] = msg
                
        # 检测当前输入与历史记录的差异
        modified_fields = []
        if prev_values['company'] and (prev_values['company'] not in user_input and 
                                      state.get('company_name') not in user_input):
            modified_fields.append('company')
        if prev_values['year'] and (prev_values['year'] not in user_input and 
                                   state.get('report_year') not in user_input):
            modified_fields.append('year')
        if prev_values.get('quarter') and (prev_values['quarter'] not in user_input and 
                                         state.get('report_quarter') not in user_input):
            modified_fields.append('quarter')
        # if prev_values['template'] and (prev_values['template'] not in user_input and 
        #                                state.get('template_name') not in user_input):
        #     modified_fields.append('template')
        if prev_values['materials'] and (prev_values['materials'] not in user_input):
            modified_fields.append('materials')

        prompt = f"""基于以下上下文分析用户意图:

对话阶段分析:
当前阶段: {state.get('conversation_phase', 'initial')} - {phase_features}
历史对话关键点:
{chr(10).join(f"- {msg}" for msg in state['conversation_history'][:-1])}

当前输入: "{user_input}"

系统状态:
- 公司: {state.get('company_name', '未设置')}
- 年份: {state.get('report_year', '未设置')}
- 季度: {state.get('report_quarter', '未设置')}
- 资料: {state.get('materials_status', '未确认')}

检测到的潜在变更字段: {modified_fields if modified_fields else "无"}

意图判断规则:
1. 如果检测到字段变更或用户提供新要素 → "element_processing"
2. 如果用户使用明确确认词(如"确认","是的","正确") → "confirmation"
3. 如果用户使用否定词(如"不对","修改","不是") → "element_processing"
4. 如果与待选公司相关 → "element_processing"
5. 其他情况(如"继续"等流程恢复) → "other"

特别注意:
- 对比当前输入与历史记录中的要素值
- 识别用户纠正/补充信息的意图
- 考虑对话阶段的典型行为模式

返回格式:
{{
  "intent": "element_processing|confirmation|other",
  "modified_fields": {modified_fields if modified_fields else "[]"},  // 确认或补充的字段
  "confidence": 0.8  // 意图判断置信度(0-1)
}}"""

        logger.debug(f"LLM提示:\n{prompt}")
        try:
            # Report progress if callback is available
            if state.get('progress_callback'):
                state['progress_callback']("正在分析用户输入...", 25)

            logger.debug("=== 开始LLM调用 ===")
            response = agent.agent.llm.invoke([HumanMessage(content=prompt)])
            content = response if type(response) == str else response.content
            logger.debug(f"LLM原始响应: {content}")
            logger.debug("=== LLM调用完成 ===")

            result = extract_last_valid_json(content)
            logger.debug(f"解析后的JSON结果: {result}")
            
            # 更新意图和修改标记
            state['intent'] = result.get('intent', 'other') if result else 'other'
            if result and 'modified_fields' in result and state.get('company_options', None):
                logger.info(f"检测到要素修改: {result['modified_fields']}")
                state['conversation_phase'] = 'collecting'  # 重置为收集阶段
                # 直接更新state中的要素值
                if 'company' in result['modified_fields']:
                    state['company_name'] = user_input
                    agent.company_name = user_input  # 同步更新agent
                    state['intent'] = 'element_processing'  # 设置为要素验证意图
                else:
                    if 'modified_fields' in result and len(result.get('modified_fields', [])) > 0:
                        state['intent'] = 'element_processing'  # 设置为要素验证意图

                
            logger.info(f"解析意图: {state['intent']}")
        except Exception as e:
            logger.error(f"意图分析失败: {str(e)}", exc_info=True)
            state['intent'] = 'other'
            state['last_message'] = f"⚠️ 系统处理您的请求时遇到问题\n\n可能原因:\n- 输入格式不符合要求\n- 网络连接问题\n- 系统临时故障\n\n建议操作:\n1. 检查输入格式\n2. 稍后输入“继续”再试\n3. 如问题持续,请联系管理员并提供错误时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
            raise WorkflowException(
                message="Intent analysis failed",
                workflow_name="report_workflow",
                step_name="intent_analysis",
                details=f"Intent processing error: {str(e)}",
                original_exception=e,
                context={'error_code': 'INTENT_ERROR'},
                suggested_action="Check input format, try again later, or contact administrator"
            )
            # Legacy message format preserved in state
            # raise RAGWorkflowError("INTENT_ERROR", f"⚠️ 系统处理您的请求时遇到问题\n\n可能原因:\n- 输入格式不符合要求\n- 网络连接问题\n- 系统临时故障\n\n建议操作:\n1. 检查输入格式\n2. 稍后输入“继续”再试\n3. 如问题持续,请联系管理员并提供错误时间: {time.strftime('%Y-%m-%d %H:%M:%S')}", str(e))
        logger.info("=== 意图分析完成 ===")
        return state

    # 2. 要素处理分支
    @log_and_reraise(logger, "report eleement extraction")
    def element_extraction(state: AgentState):
        """提取四要素信息"""
        logger.info("=== 要素提取开始 ===")
        try:
            user_input = state['last_message']
        except KeyError:
            logger.error("Missing 'last_message' in state")
            state['last_message'] = "系统错误: 缺少对话输入"
            return state
        logger.debug(f"用户输入: {user_input}")
        
        # 恢复公司选项状态
        if state.get('company_options'):
            agent.company_options = state['company_options']
            logger.debug(f"恢复公司选项: {len(agent.company_options)}条")

        # 首先保留现有的有效值
        existing_values = {
            'company': agent.company_name,
            'year': agent.report_year,
            'quarter': getattr(agent, 'report_quarter', None),  # 使用getattr防止属性不存在
            # 'template': agent.required_files['template'],
            'materials': state['materials_status']
        }
        logger.debug(f"现有值: {existing_values}")

        # 仅当输入包含JSON时才尝试解析
        if '{' in user_input and '}' in user_input:
            try:
                logger.debug("尝试从输入中提取JSON")
                elements = extract_last_valid_json(user_input)
                logger.debug(f"提取的JSON: {elements}")

                # 合并新值和现有值，新值优先
                merged = {**existing_values, **elements}
                logger.debug(f"合并后的值: {merged}")

                updated_fields = []
                # 更新agent状态
                if merged.get('company'):
                    agent.company_name = merged['company']
                    logger.debug(f"更新公司名称: {merged['company']}")
                    updated_fields.append(f"更新公司名称: {merged['company']}")
                if merged.get('year'):
                    agent.report_year = merged['year']
                    logger.debug(f"更新报告年份: {merged['year']}")
                    updated_fields.append(f"更新报告年份: {merged['year']}")
                if merged.get('quarter'):
                    agent.report_quarter = merged['quarter']
                    logger.debug(f"更新报告季度: {merged['quarter']}")
                    updated_fields.append(f"更新报告季度: {merged['quarter']}")
              
                if merged.get('materials'):
                    state['materials_status'] = merged['materials']
                    logger.debug(f"更新资料状态: {merged['materials']}")
                    return state

                if updated_fields:
                    state['last_message'] = "\n".join(updated_fields)
                    return state
            except Exception as e:
                logger.warning("JSON解析失败，继续LLM提取", exc_info=True)
                
        # 如果没有JSON或解析失败，使用LLM提取缺失的值
        missing_fields = []
        if not agent.company_name:
            missing_fields.append("1. 公司名称(保持与用户输入一致，不能改变语言)")
        if not agent.report_year:
            missing_fields.append("2. 报告年份(YYYY)")
        # 季度是可选的，如果用户提供了年份但没有季度，可以提示用户提供
        if agent.report_year and not getattr(agent, 'report_quarter', None):
            missing_fields.append("3. 报告季度(Q1/Q2/Q3/Q4)或‘全年’")
        # if not agent.required_files['template']:
        #     missing_fields.append("3. 模板名称/类型(风险报告)")
        if state['materials_status'] == 'missing':
            missing_fields.append("4. 资料状态(exists/missing/confirmed_empty)")

        # if missing_fields:
        prompt = f"""从用户输入提取或修改报告要素：
        {user_input}
        
        当前要素状态:
        {"公司: " + f"{agent.company_name}" if agent.company_name else "未设置"}
        {"年份: " + f"{agent.report_year}" if agent.report_year else "未设置"}
        {"季度: " + f"{getattr(agent, 'report_quarter', None)}" if hasattr(agent, 'report_quarter') and agent.report_quarter else "未设置"}
        {"资料状态: " + f"{state['materials_status']}" if state['materials_status'] != 'missing' else "未确认"}
        
        操作规则:
        1. 如果用户提供新要素值 → 更新对应字段
        2. 如果用户要修改已有要素 → 用新值替换旧值
        3. 如果用户未提及 → 保持原值不变
        
        需要处理的要素(返回英文键名):
        {chr(10).join(f"{i + 1}. {field.split('. ')[1]}" for i, field in enumerate(missing_fields)) if missing_fields else "  请从用户输入：<{user_input}> 中识别要素"}
        
        返回格式要求:
        - 严格返回纯JSON格式
        - 不要包含```json```等代码块标记
        - 只包含需要更新的字段
        - 示例: {{"company": "新公司名称", "year": "2025"}}
        
        返回JSON格式: {{
            {', '.join(f'"{"company" if "公司" in field else "year" if "年份" in field else "company"}": "..."' for field in missing_fields)}
        }}"""

        logger.debug(f"LLM提示:\n{prompt}")
        try:
            # Report progress if callback is available
            if state.get('progress_callback'):
                state['progress_callback']("正在验证公司信息...", 50)

            logger.debug("=== 开始LLM调用 ===")
            response = agent.agent.llm.invoke([HumanMessage(content=prompt)])
            content = response if type(response) == str else response.content
            logger.debug(f"LLM原始响应: {content}")
            logger.debug("=== LLM调用完成 ===")

            # Attempt to parse JSON and capture raw content on failure
            try:
                elements = extract_last_valid_json(content)
            except Exception as parse_error:
                logger.error(f"JSON解析失败: {str(parse_error)}", exc_info=True)
                logger.error(f"原始响应内容: {content}")
                raise  # Re-raise the exception after logging
            logger.debug(f"解析后的JSON结果: {elements}")
            logger.debug(f"解析的要素: {elements}")

            # 只更新缺失的字段
            if not elements:
                logger.debug("LLM提取失败，返回空，跳过更新")
                return state
            if elements.get('company'):
                agent.company_name = elements['company']
                state['company_name'] = elements['company']
                logger.info(f"设置公司名称: {elements['company']}")
            if elements.get('year'):
                agent.report_year = elements['year']
                state['report_year'] = elements['year']
                logger.info(f"设置报告年份: {elements['year']}")
            if elements.get('quarter'):
                # Ensure quarter is always stored as a string
                quarter_value = str(elements['quarter'])
                agent.report_quarter = quarter_value
                state['report_quarter'] = quarter_value
                logger.info(f"设置报告季度: {quarter_value}")
                # 记录季度信息的来源和处理过程
                logger.debug(f"季度信息来源: LLM提取")
                logger.debug(f"季度信息处理: 从JSON结果中提取季度值 -> {quarter_value}")
                logger.debug(f"更新后的agent.report_quarter: {agent.report_quarter}")
                logger.debug(f"更新后的state['report_quarter']: {state['report_quarter']}")
            elif hasattr(agent, 'report_quarter') and agent.report_quarter:
                # Preserve existing quarter value if not being updated
                state['report_quarter'] = agent.report_quarter
                logger.debug(f"保留现有季度值: {agent.report_quarter}")



            if elements.get('materials'):
                state['materials_status'] = elements['materials']
                logger.info(f"设置资料状态: {elements['materials']}")
        except Exception as e:
            logger.error(f"要素提取失败: {str(e)}", exc_info=True)
            state['last_message'] = f"""⚠️ 提取报告要素时出错
                错误详情: {str(e)}

                建议操作:
                1. 检查输入的公司名称、年份等信息格式是否正确
                2. 检查资料文件类型是否正确
                3. 如问题持续,请提供以下信息联系管理员:
                - 错误时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
                - 您输入的内容: {user_input[:100]}..."""
            raise DataProcessingException(
                message="报告要素提取失败",
                details=f"错误详情: {str(e)}",
                suggested_action="请检查公司名称、年份等是否正确并点击'继续'重试",
                context={'operation': 'element_extraction', 'error_type': 'processing_error'}
            )

        logger.info("=== 要素提取完成 ===")
        return state

    @log_and_reraise(logger, "company name validation")
    def company_ensurance(state: AgentState):
        """公司名称验证与确认 with unified error handling"""
        set_operation_context("company_validation")
        logger.info("=== 公司名称验证开始 ===")
        logger.debug(f"输入state完整内容: {state}")

        with error_boundary("company name validation", LayerType.BUSINESS_LOGIC):
            if not agent.company_name and not state.get('company_name'):
                logger.debug(f"公司名称为空，跳过验证")
                return state

            company_name = state.get('company_name') or agent.company_name
            if not company_name:
                raise ValidationException(
                    message="Company name is required",
                    field_name="company_name",
                    details="Company name cannot be empty for validation",
                    suggested_action="Please provide a valid company name"
                )
        # 使用爬虫客户端获取公司列表
        logger.debug(f"开始查询公司: {company_name}")
        agent.company_name = company_name
        # 使用爬虫客户端获取公司列表
        workspace_path = f"workspaces/{agent.workspace_id}"
        logger.debug(f"开始核查公司详情: {company_name} to {workspace_path}")
        if os.path.exists(f"{workspace_path}/{company_name}.md"):
            state['company_options'] = None
            agent.company_options = None
            logger.debug(f"公司详情已查询存入: {workspace_path}/{company_name}.md")
            return state
            
        client = CrawlerClient()
        try:
            result = asyncio.run(client.start_crawl("https://www.qcc.com", company_name))
            logger.debug(f"爬虫返回状态码: {result.get('code')}")
            code = result.get("code", 500)
            logger.debug(f"爬虫返回code：{code}")
            if int(code / 10) != 20:
                raise ExternalServiceException(
                    message="公司验证失败",
                    service_name="crawler",
                    details=f"错误详情: {result.get('status', '处理您的请求时遇到问题，请检查输入或输入“继续”重试')}",
                    suggested_action="爬虫状态异常，请‘reset crawler’",
                    context={'error_code': REPORT_ERROR_CODES['COMPANY_VALIDATION_FAILED']}
                )
        except httpx.HTTPStatusError as e:
            logger.error(f"[{REPORT_ERROR_CODES['CRAWLER_ERROR']}] 公司验证失败: {str(e)}", exc_info=True)
            raise ExternalServiceException(
                message="公司验证失败",
                service_name="crawler",
                details=f"错误详情: {str(e)}",
                suggested_action="爬虫状态异常，请‘reset crawler’",
                context={'error_code': REPORT_ERROR_CODES['CRAWLER_ERROR']}
            )
        except ExternalServiceException:
            raise
        except Exception as e:
            logger.error(f"[{REPORT_ERROR_CODES['COMPANY_VALIDATION_FAILED']}] 公司验证失败: {str(e)}", exc_info=True)
            raise ValidationException(
                message="公司验证失败",
                field_name="company_name",
                details=f"错误详情: {str(e)}",
                suggested_action="请检查公司名称是否正确并点击'继续'重试",
                context={'error_code': REPORT_ERROR_CODES['COMPANY_VALIDATION_FAILED']}
            )
        
        companies = result.get("companies", [])
        logger.debug(f"查询结果: {companies}")
        
        if len(companies) > 1:
            # 更新state对象并确保所有字段被保留
            updated_state = {
                **state,
                "company_options": companies,
                "conversation_phase": "verifying"  # 设置对话阶段为验证阶段
            }
            logger.info(f"找到多个匹配公司: {len(companies)}个")
            logger.debug(f"更新后的state: {updated_state}")
            
            # 同步更新agent状态
            agent.company_options = companies
            agent.conversation_phase = "verifying"
            return updated_state
        elif len(companies) == 1:
            page_content = result.get("page_content", None)
            agent.company_name = companies[0]
            state['company_name'] = companies[0]
            state['company_options'] = companies
            logger.info(f"唯一匹配公司: {companies[0]}")
            if page_content:
                os.makedirs(workspace_path, exist_ok=True)
                logger.debug(f"输出公司: {workspace_path}")
                with open(f"{workspace_path}/{companies[0]}.md", "w", encoding="utf-8") as f:
                    f.write(page_content)
        else:
            logger.warning(f"[{REPORT_ERROR_CODES['COMPANY_VALIDATION_FAILED']}] 未找到匹配公司")
            raise ValidationException(
                message="未找到匹配公司",
                field_name="company_name",
                details=f"查询的公司: {company_name}",
                suggested_action="请检查公司名称是否正确并点击'继续'重试",
                context={'error_code': REPORT_ERROR_CODES['COMPANY_VALIDATION_FAILED']}
            )
            
        logger.info("=== 公司名称验证完成 ===")
        return state
    
    @log_and_reraise(logger, "company wcc content crawling")
    def company_crawl(state: AgentState):
        """公司qcc数据"""
        logger.info("=== 公司详情查询开始 ===")
        
        if not agent.company_name:
            logger.debug("公司名称为空，跳过qcc")
            return state
            
        # 使用爬虫客户端获取公司列表
        workspace_path = f"workspaces/{agent.workspace_id}"
        if os.path.exists(f"{workspace_path}/{agent.company_name}.md"):
            state['company_options'] = None
            agent.company_options = None
            return state

        logger.debug(f"开始查询公司详情: {agent.company_name} to {workspace_path}")
        client = CrawlerClient()
        try:
            result = asyncio.run(client.start_crawl("https://www.qcc.com", agent.company_name))
        except Exception as e:
            logger.error(f"[{REPORT_ERROR_CODES['CRAWLER_ERROR']}] 获取公司数据失败: {str(e)}", exc_info=True)
            raise ExternalServiceException(
                message="获取公司数据失败",
                service_name="crawler",
                details=f"错误详情: {str(e)}",
                suggested_action="爬虫状态异常，请‘reset crawler’",
                context={'error_code': REPORT_ERROR_CODES['CRAWLER_ERROR']}
            )
            
        qcc = result.get("page_content", "")
        logger.debug(f"查询qcc结果: {len(qcc)} bytes")
        workspace_path = f"workspaces/{agent.workspace_id}"
        os.makedirs(workspace_path, exist_ok=True)
        logger.debug(f"输出crawl公司: {workspace_path}")
        with open(f"{workspace_path}/{agent.company_name}.md", "w", encoding='utf-8') as f:
            f.write(qcc)
        
        # Clear all company selection related states
        state['company_options'] = None
        state['company_crawled'] = True
        # Sync with agent
        if hasattr(agent, 'company_options'):
            agent.company_options = None
        # Also clear conversation history of company options
        if state.get('conversation_history'):
            state['conversation_history'] = [
                msg for msg in state['conversation_history'] 
                if not (isinstance(msg, dict) and 'company_options' in msg)
            ]
        # Reset conversation phase
        state['conversation_phase'] = 'collecting'
            
        logger.info("=== 公司名称验证完成 ===")
        return state

    @log_and_reraise(logger, "company selection")
    def company_prompt(state: AgentState):
        """公司选择提示"""
        logger.info("=== 公司选择提示开始 ===")
        
        logger.debug(f"当前state完整内容: {state}")
        logger.debug(f"state包含的键: {state.keys()}")
        
        if not state.get("company_options") or len(state.get("company_options")) == 1:
            logger.debug(f"无公司选项，跳过提示. company_options: {state.get('company_options')}")
            logger.debug(f"当前state中所有字段: {state}")
            state['company_options'] = None
            agent.company_options = None
            
            return user_prompt(element_validation(state))

            
        # 确保company_options同步到agent实例
        agent.company_options = state["company_options"]
        options = "\n".join(
            f"{i+1}. {company}" 
            for i, company in enumerate(state["company_options"])
        )
        
        message = f"""请从以下选项中选择正确的公司:
        
{options}

请输入对应编号或完整公司名称"""
        
        state["last_message"] = message
        logger.info("生成公司选择提示")
        logger.info("=== 公司选择提示完成 ===")
        return state
    
    @log_and_reraise(logger, "reporting elements validation")
    def element_validation(state: AgentState):
        """验证要素完整性并生成缺失提示"""
        logger.info("=== 要素验证开始 ===")
        missing = {}
        logger.debug(
            f"检测当前状态: company={agent.company_name}, year={agent.report_year}, materials={state['materials_status']}")

        # 检查公司名称
        if not agent.company_name:
            missing['company'] = "公司名称未提供"
            logger.debug("公司名称缺失")

        # 检查年份
        if not agent.report_year:
            missing['year'] = "报告年份未提供"
            logger.debug("报告年份缺失")
            
        # 检查季度（可选）
        if agent.report_year and not getattr(agent, 'report_quarter', None):
            logger.debug("报告季度未提供(可选)")

        # 检查模板
        # if not agent.required_files['template']:
        #     templates = list(Path("templates").glob("*.md"))
        #     if not templates:
        #         missing['template'] = "模板目录为空，请上传模板文件"
        #         logger.warning("模板目录为空")
        #     elif len(templates) == 1:
        #         # Auto-use single template
        #         matched = templates[0].name
        #         agent.required_files['template'] = matched
        #         state['template_file'] = matched
        #         logger.info(f"自动使用唯一模板: {matched}")
        #     else:
        #         missing['template'] = "未匹配到合适报告模板，请指定"
        #         logger.debug("模板未指定")

        # 检查资料
        if agent.company_name and agent.report_year and state['materials_status'] != 'confirmed_empty':
            workspace_path = Path(f"workspaces/{agent.workspace_id}/{agent.company_name}/{agent.report_year}")
            # # 获取目录中所有有效文件（包括PDF/Excel和Markdown）
            # existing_files = [
            #     f.name for f in workspace_path.glob("*.*") 
            #     if f.suffix.lower() in ('.pdf', '.xls', '.xlsx')
            # ]
            # # 合并去重后更新到agent状态
            # current_files = agent.required_files['collections']
            # agent.required_files['collections'] = list(set(current_files + existing_files))
            existing_files, materials_status = agent.check_workspace_files()
            state['materials_status'] = materials_status

            if not existing_files:
                missing['materials'] = "缺少资料文件"
                logger.debug("资料文件缺失")
            else:
                state['materials_status'] = 'exists'  # 更新资料状态为存在
                logger.debug(f"发现资料文件: {existing_files}")

        state['missing_elements'] = missing
        logger.info(f"缺失要素: {missing}")
        logger.info("=== 要素验证完成 ===")
        return state
    
    @log_and_reraise(logger, "ueser prompt generation")
    def user_prompt(state: AgentState):
        """生成用户提示"""
        logger.info("=== 生成用户提示开始 ===")
        if not state['missing_elements']:
            # 所有要素齐全，生成确认提示
            # Format file list as markdown bullets
            file_list = ""
            if state['materials_status'] == 'exists':
                files = agent.required_files['collections']
                file_list = "\n".join(f"- {Path(f).name}" for f in files if Path(f).is_file)

            # 准备季度显示信息
            quarter_info = f"📊 **报告季度**: {state.get('report_quarter') or getattr(agent, 'report_quarter', None)}  \n" if (state.get('report_quarter') or getattr(agent, 'report_quarter', None)) else ""
            
            message = f"""✅ **所有报告要素已完备！请确认以下信息：**\n
\n
🏢 **公司名称**: {agent.company_name}  \n
📅 **报告年份**: {agent.report_year}  \n
{quarter_info}
📂 **已上传资料**:  \n
{file_list}\n

请回复「确认」开始生成报告，或「修改」进行调整"""
            state['last_message'] = message
            logger.info("生成确认提示")
        else:
            # 生成缺失提示
            prompts = ["请补充以下报告信息："]
            for field, msg in state['missing_elements'].items():
                prompts.append(f"- {msg}")

            if 'materials' in state['missing_elements']:
                prompts.append("\n请上传资料文件，回复'继续'")

            message = "\n".join(prompts)
            state['last_message'] = message
            logger.info(f"生成缺失提示: {message}")

        logger.debug(f"最终消息: {state['last_message']}")
        logger.info("=== 生成用户提示完成 ===")
        return state

    @log_and_reraise(logger, "report generation final confirmation")
    def final_confirmation(state: AgentState):
        """处理最终确认"""
        logger.info("=== 最终确认处理开始 ===")
        user_input = state['last_message']
        logger.debug(f"用户确认输入: {user_input}")

        # 检查工作区文件并更新状态
        files, materials_status = agent.check_workspace_files()
        state['materials_status'] = materials_status

        # 使用LLM分析确认意图
        prompt = f"""分析用户确认意图：
        {user_input}
        
        当前报告要素状态：
        - 公司名称：{agent.company_name or '未设置'}
        - 报告年份：{agent.report_year or '未设置'}
        - 资料状态：{'已上传' if state['materials_status'] == 'exists' else '确认无资料' if state['materials_status'] == 'confirmed_empty' else '未确认'}
        
        判断规则：
        - 如果用户明确确认所有要素正确 → 返回 {{"confirmed": true}}
        - 如果用户要修改某些要素 → 返回 {{"confirmed": false, "message": "要修改的要素说明"}}
        - 如果不确定 → 返回 {{"confirmed": false, "message": "请确认以下要素是否正确..."}}
        
        严格返回纯JSON格式，不要包含```json```等代码块标记"""

        try:
            # Report progress if callback is available
            if state.get('progress_callback'):
                state['progress_callback']("正在生成报告响应...", 75)

            logger.debug(f"LLM提示:\n{prompt}")
            logger.debug("=== 开始LLM调用 ===")
            response = agent.agent.llm.invoke([HumanMessage(content=prompt)])
            content = response if type(response) == str else response.content
            logger.debug(f"LLM原始响应: {content}")
            logger.debug("=== LLM调用完成 ===")

            result = extract_last_valid_json(content)
            logger.debug(f"解析后的JSON结果: {result}")

            if result.get('confirmed'):
                state['last_message'] = "开始生成报告..."
                state['confirmed'] = True
                logger.info("用户确认成功")
            else:
                # 显示当前要素并询问如何修改
                message = result.get('message', '请确认以下报告要素：')
                # 准备文件列表显示
                files, materials_status = agent.check_workspace_files()
                state['materials_status'] = materials_status
                file_display = ""
                if state['materials_status'] == 'exists':
                    file_display = "\n📄 资料文件:\n" + "\n".join(f"　　- {f}" for f in files)
                
                # 准备季度显示信息
                quarter_info = f"📊 报告季度：{agent.report_quarter}" if hasattr(agent, 'report_quarter') and agent.report_quarter else ""
                
                state['last_message'] = f"""{message}
                
当前要素：
🏢 公司名称：{agent.company_name or '未设置'}
📅 报告年份：{agent.report_year or '未设置'} 
{quarter_info}
📂 资料状态：{'已上传' if state['materials_status'] == 'exists' else '确认无资料' if state['materials_status'] == 'confirmed_empty' else '未确认'}
{file_display}

请回复要修改的要素或「确认」继续"""
                state['confirmed'] = False  # 明确标记为未确认
                logger.info("用户需要修改要素或确认不明确")

        except Exception as e:
            logger.error(f"确认分析失败: {str(e)}", exc_info=True)
            state['last_message'] = f"""⚠️ 确认处理失败

错误详情: {str(e)}

建议操作:
1. 检查输入是否符合要求
2. 输入“继续”重试
3. 如问题持续,请联系管理员并提供错误时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"""

        logger.info("=== 最终确认处理完成 ===")
        return state

    @log_and_reraise(logger, "intention unclear")
    def other_processing(state: AgentState):
        """处理其他请求"""
        logger.info("=== 其他请求处理开始 ===")

        # 准备季度显示信息
        quarter_info = f"📊 报告季度：{state.get('report_quarter') or getattr(agent, 'report_quarter', None)}" if (state.get('report_quarter') or getattr(agent, 'report_quarter', None)) else ""
        
        message = f"""当前报告要素状态：
🏢 公司名称：{agent.company_name or '未设置'}
📅 报告年份：{agent.report_year or '未设置'}
{quarter_info}
📂 资料状态：{'已上传' if state['materials_status'] == 'exists' else '确认无资料' if state['materials_status'] == 'confirmed_empty' else '未确认'}

请提供报告相关要素信息或确认请求"""

        state['last_message'] = message
        logger.info("处理非要素相关请求并显示当前要素")
        logger.info("=== 其他请求处理完成 ===")
        return state

#     def match_template(user_input: str) -> Optional[str]:
#         """使用LLM语义匹配模板文件"""
#         from pathlib import Path
#         from langchain_core.messages import HumanMessage

#         templates = [f.name for f in Path("templates").glob("*.md")]
#         if not templates:
#             return None

#         if not agent or not hasattr(agent, 'agent') or not hasattr(agent.agent, 'llm'):
#             # Fallback to simple matching if no LLM available
#             from difflib import get_close_matches
#             matches = get_close_matches(user_input, [f.split('.')[0] for f in templates], n=1, cutoff=0.6)
#             return matches[0] if matches else None

#         # Prepare prompt for LLM
#         prompt = f"""请根据用户需求选择最合适的报告模板：

# 用户需求描述: {user_input}

# 可选模板列表:
# {chr(10).join(f"- {t}" for t in templates)}

# 选择标准:
# 1. 模板文件名与用户要求的报告的语义匹配度
# 2. 模板文件名与用户指定模板文件名关键词的相关性
# 3. 模板的通用性

# 请直接返回选择的模板文件名，不要包含其他内容或解释。"""

#         try:
#             logger.debug(f"LLM提示:\n{prompt}")
#             logger.debug("=== 开始LLM调用 ===")
#             response = agent.agent.llm.invoke([HumanMessage(content=prompt)])
#             logger.debug(f"LLM原始响应: {response.content}")
#             logger.debug("=== LLM调用完成 ===")

#             selected = response.content.strip()
#             logger.debug(f"选择的模板: {selected}")

#             # Validate the selected template exists
#             if selected in templates:
#                 return selected
#             return None
#         except Exception as e:
#             logging.error(f"模板匹配失败: {str(e)}")
#             return None

    def route_by_intent(state):
        """路由意图"""
        intent = state['intent'] or 'other'
        logger.debug(f"路由意图: {intent}")
        if intent == 'company_crawl':
            return 'company_crawl'
        return intent

    """创建符合新需求的工作流"""
    # 简化StateGraph初始化，依赖TypedDict自动处理字段
    workflow = StateGraph(AgentState)
    logger.debug(f"工作流初始化完成，state schema包含字段: {AgentState.__annotations__.keys()}")

    workflow.add_node("intent_analysis", intent_analysis)
    workflow.add_node("element_extraction", element_extraction)
    workflow.add_node("company_ensurance", company_ensurance)
    workflow.add_node("company_crawl", company_crawl)
    workflow.add_node("company_prompt", company_prompt)
    workflow.add_node("element_validation", element_validation)
    workflow.add_node("user_prompt", user_prompt)
    workflow.add_node("final_confirmation", final_confirmation)
    workflow.add_node("other_processing", other_processing)
    logger.info("所有工作流节点已添加")

    # Set up conditional edges with all possible intents
    workflow.add_conditional_edges(
        "intent_analysis",
        route_by_intent,
        {
            "element_processing": "element_extraction",
            "confirmation": "final_confirmation",
            "other": "other_processing",
            "company_selection": "element_extraction",
            "company_crawl": "company_crawl"  # Explicitly add company_crawl route
        }
    )
    logger.info("条件边已设置")

    # 要素处理流程
    workflow.add_edge("element_extraction", "company_ensurance")
    workflow.add_edge("company_crawl", "element_validation")
    
    # 公司验证后的条件分支
    def route_after_company_check(state):
        # Skip prompt if we just completed a crawl
        if state.get('company_crawled'):
            return "element_validation"
        # Only show prompt for genuine multiple options
        if state.get("company_options"):
            return "company_prompt"
        return "element_validation"
        
    workflow.add_conditional_edges(
        "company_ensurance",
        route_after_company_check,
        {
            "company_prompt": "company_prompt",
            "element_validation": "element_validation"
        }
    )
    
    workflow.add_edge("company_prompt", END)
    workflow.add_edge("element_validation", "user_prompt")
    workflow.add_edge("user_prompt", END)  # 返回用户交互
    logger.info("要素处理流程边已设置")

    # 确认流程
    workflow.add_edge("final_confirmation", END)
    logger.info("确认流程边已设置")

    # 其他流程
    workflow.add_edge("other_processing", END)
    logger.info("其他流程边已设置")

    workflow.set_entry_point("intent_analysis")
    logger.info("工作流配置完成")

    return workflow.compile()
